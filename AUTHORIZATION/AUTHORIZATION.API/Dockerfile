# syntax=docker/dockerfile:1

# Dockerfile for Authorization API Service
# E-Commerce Teddy Bear Shop - Authorization API

################################################################################
# Build stage
FROM --platform=$BUILDPLATFORM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build

WORKDIR /source

# Copy solution file and all project files for dependency resolution
COPY *.sln ./
COPY E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults/*.csproj ./E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults/
COPY AUTHORIZATION/AUTHORIZATION.API/*.csproj ./AUTHORIZATION/AUTHORIZATION.API/

# Restore dependencies
RUN --mount=type=cache,id=nuget,target=/root/.nuget/packages \
    dotnet restore AUTHORIZATION/AUTHORIZATION.API/AUTHORIZATION.API.csproj

# Copy the entire source code
COPY . .

# Build argument for target architecture
ARG TARGETARCH

# Build and publish the Authorization API
RUN --mount=type=cache,id=nuget,target=/root/.nuget/packages \
    dotnet publish AUTHORIZATION/AUTHORIZATION.API/AUTHORIZATION.API.csproj \
    -a ${TARGETARCH/amd64/x64} \
    --use-current-runtime \
    --self-contained false \
    -o /app \
    --no-restore

################################################################################
# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS final

# Install curl for health checks
RUN apk add --no-cache curl

# Create app user
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

WORKDIR /app

# Copy the published application from build stage
COPY --from=build /app .

# Create logs directory and set permissions
RUN mkdir -p /app/logs && \
    chown -R appuser:appgroup /app

# Switch to non-privileged user
USER appuser

# Expose port
EXPOSE 8080

ENTRYPOINT ["dotnet", "AUTHORIZATION.API.dll"]
