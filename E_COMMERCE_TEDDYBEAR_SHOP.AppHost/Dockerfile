# syntax=docker/dockerfile:1

# Dockerfile for Aspire AppHost
# E-Commerce Teddy Bear Shop - AppHost

################################################################################
# Build stage
FROM --platform=$BUILDPLATFORM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build

WORKDIR /source

# Copy solution file and all project files for dependency resolution
COPY *.sln ./
COPY E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults/*.csproj ./E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults/
COPY CONTRACT/CONTRACT/*.csproj ./CONTRACT/CONTRACT/
COPY COMMAND/COMMAND.API/*.csproj ./COMMAND/COMMAND.API/
COPY COMMAND/COMMAND.APPLICATION/*.csproj ./COMMAND/COMMAND.APPLICATION/
COPY COMMAND/COMMAND.CONTRACT/*.csproj ./COMMAND/COMMAND.CONTRACT/
COPY COMMAND/COMMAND.INFRASTRUCTURE/*.csproj ./COMMAND/COMMAND.INFRASTRUCTURE/
COPY COMMAND/COMMAND.PERSISTENCE/*.csproj ./COMMAND/COMMAND.PERSISTENCE/
COPY COMMAND/COMMAND.PRESENTATION/*.csproj ./COMMAND/COMMAND.PRESENTATION/
COPY QUERY/QUERY.API/*.csproj ./QUERY/QUERY.API/
COPY QUERY/QUERY.APPLICATION/*.csproj ./QUERY/QUERY.APPLICATION/
COPY QUERY/QUERY.CONTRACT/*.csproj ./QUERY/QUERY.CONTRACT/
COPY QUERY/QUERY.INFRASTRUCTURE/*.csproj ./QUERY/QUERY.INFRASTRUCTURE/
COPY QUERY/QUERY.PERSISTENCE/*.csproj ./QUERY/QUERY.PERSISTENCE/
COPY QUERY/QUERY.PRESENTATION/*.csproj ./QUERY/QUERY.PRESENTATION/
COPY AUTHORIZATION/AUTHORIZATION.API/*.csproj ./AUTHORIZATION/AUTHORIZATION.API/
COPY E_COMMERCE_TEDDYBEAR_SHOP.AppHost/*.csproj ./E_COMMERCE_TEDDYBEAR_SHOP.AppHost/

# Restore dependencies
RUN --mount=type=cache,id=nuget,target=/root/.nuget/packages \
    dotnet restore E_COMMERCE_TEDDYBEAR_SHOP.AppHost/E_COMMERCE_TEDDYBEAR_SHOP.AppHost.csproj

# Copy the entire source code
COPY . .

# Build argument for target architecture
ARG TARGETARCH

# Build and publish the AppHost
RUN --mount=type=cache,id=nuget,target=/root/.nuget/packages \
    dotnet publish E_COMMERCE_TEDDYBEAR_SHOP.AppHost/E_COMMERCE_TEDDYBEAR_SHOP.AppHost.csproj \
    -a ${TARGETARCH/amd64/x64} \
    --use-current-runtime \
    --self-contained false \
    -o /app \
    --no-restore

################################################################################
# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS final

# Install curl for health checks
RUN apk add --no-cache curl

# Create app user
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

WORKDIR /app

# Copy the published application from build stage
COPY --from=build /app .

# Create logs directory and set permissions
RUN mkdir -p /app/logs && \
    chown -R appuser:appgroup /app

# Switch to non-privileged user
USER appuser

# Expose port
EXPOSE 8080

ENTRYPOINT ["dotnet", "E_COMMERCE_TEDDYBEAR_SHOP.AppHost.dll"]
